# CrowdSec setup additions

# Directories
mkdir -p /host-setup/config/crowdsec/db
mkdir -p /host-setup/config/crowdsec/acquis.d
mkdir -p /host-setup/config/traefik/logs
mkdir -p /host-setup/config/traefik/conf
mkdir -p /host-setup/config/crowdsec_logs

# Config files
cat > /host-setup/config/crowdsec/acquis.yaml << 'EOF'
poll_without_inotify: false
filenames:
  - /var/log/traefik/*.log
labels:
  type: traefik
---
listen_addr: 0.0.0.0:7422
appsec_config: crowdsecurity/appsec-default
name: myAppSecComponent
source: appsec
labels:
  type: appsec
EOF

cat > /host-setup/config/crowdsec/profiles.yaml << 'EOF'
name: captcha_remediation
filters:
  - Alert.Remediation == true && Alert.GetScope() == "Ip" && Alert.GetScenario() contains "http"
decisions:
  - type: captcha
    duration: 4h
on_success: break

---
name: default_ip_remediation
filters:
 - Alert.Remediation == true && Alert.GetScope() == "Ip"
decisions:
 - type: ban
   duration: 4h
on_success: break

---
name: default_range_remediation
filters:
 - Alert.Remediation == true && Alert.GetScope() == "Range"
decisions:
 - type: ban
   duration: 4h
on_success: break
EOF

wget -O /host-setup/config/traefik/conf/captcha.html https://gist.githubusercontent.com/hhftechnology/48569d9f899bb6b889f9de2407efd0d2/raw/captcha.html

# Function to add CrowdSec routers and services to existing dynamic_config.yml
add_crowdsec_config() {
    echo "🔧 Adding CrowdSec configuration to Traefik dynamic config..."

    local config_file="/host-setup/config/traefik/rules/dynamic_config.yml"

    if [ ! -f "$config_file" ]; then
        echo "⚠️ Warning: dynamic_config.yml not found at $config_file"
        return 1
    fi

    # Create a temporary file for the new configuration
    local temp_file="/tmp/dynamic_config_crowdsec_temp.yml"

    # Read the existing file and add CrowdSec routers and services
    awk '
    /^  services:/ {
        # We found the services section, add routers before it
        print "  routers:"
        print "    # HTTP to HTTPS redirect router"
        print "    main-app-router-redirect:"
        print "      rule: \"Host(\\`" ENVIRON["ADMIN_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`)\""
        print "      service: next-service"
        print "      entryPoints:"
        print "        - web"
        print "      middlewares:"
        print "        - redirect-to-https"
        print ""
        print "    # Next.js router (handles everything except API and WebSocket paths)"
        print "    next-router:"
        print "      rule: \"Host(\\`" ENVIRON["ADMIN_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`) && !PathPrefix(\\`/api/v1\\`)\""
        print "      service: next-service"
        print "      entryPoints:"
        print "        - websecure"
        if (ENVIRON["STATIC_PAGE_SUBDOMAIN"]) {
            print "      middlewares:"
            print "        - security-headers"
        }
        print "      tls:"
        print "        certResolver: letsencrypt"
        print ""
        print "    # API router (handles /api/v1 paths)"
        print "    api-router:"
        print "      rule: \"Host(\\`" ENVIRON["ADMIN_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`) && PathPrefix(\\`/api/v1\\`)\""
        print "      service: api-service"
        print "      entryPoints:"
        print "        - websecure"
        if (ENVIRON["STATIC_PAGE_SUBDOMAIN"]) {
            print "      middlewares:"
            print "        - security-headers"
        }
        print "      tls:"
        print "        certResolver: letsencrypt"
        print ""
        print "    # WebSocket router"
        print "    ws-router:"
        print "      rule: \"Host(\\`" ENVIRON["ADMIN_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`)\""
        print "      service: api-service"
        print "      entryPoints:"
        print "        - websecure"
        if (ENVIRON["STATIC_PAGE_SUBDOMAIN"]) {
            print "      middlewares:"
            print "        - security-headers"
        }
        print "      tls:"
        print "        certResolver: letsencrypt"
        print ""
        if (ENVIRON["STATIC_PAGE_SUBDOMAIN"]) {
            print "    statiq-router-redirect:"
            print "      rule: \"Host(\\`" ENVIRON["STATIC_PAGE_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`)\""
            print "      service: statiq-service"
            print "      entryPoints:"
            print "        - web"
            print "      middlewares:"
            print "        - redirect-to-https"
            print ""
            print "    statiq-router:"
            print "      entryPoints:"
            print "        - websecure"
            print "      middlewares:"
            print "        - statiq"
            print "      service: statiq-service"
            print "      priority: 100"
            print "      rule: \"Host(\\`" ENVIRON["STATIC_PAGE_SUBDOMAIN"] "." ENVIRON["DOMAIN"] "\\`)\""
            print ""
        }
        print "    # Add these lines for mcpauth"
        print "    # mcpauth http redirect router"
        print "    mcpauth-router-redirect:"
        print "      rule: \"Host(\\`oauth." ENVIRON["DOMAIN"] "\\`)\""
        print "      service: mcpauth-service"
        print "      entryPoints:"
        print "        - web"
        print "      middlewares:"
        print "        - redirect-to-https"
        print ""
        print "    # mcpauth router"
        print "    mcpauth:"
        print "      rule: \"Host(\\`oauth." ENVIRON["DOMAIN"] "\\`)\""
        print "      service: mcpauth-service"
        print "      entryPoints:"
        print "        - websecure"
        print "      tls:"
        print "        certResolver: letsencrypt"
        print ""
        print $0  # Print the services line
        next
    }
    /^    api-service:/ {
        # We found the api-service, add our services after the existing ones
        print $0
        getline; print  # url line
        getline; print  # servers line
        getline; print  # url line
        print ""
        if (ENVIRON["STATIC_PAGE_SUBDOMAIN"]) {
            print "    statiq-service:"
            print "      loadBalancer:"
            print "        servers:"
            print "          - url: \"noop@internal\""
            print ""
        }
        print "    mcpauth-service:"
        print "      loadBalancer:"
        print "        servers:"
        print "          - url: \"http://mcpauth:11000\"  # mcpauth auth server"
        print ""
        print "    oauth-service:"
        print "      loadBalancer:"
        print "        servers:"
        print "          - url: \"https://oauth." ENVIRON["DOMAIN"] "\""
        next
    }
    { print }
    ' "$config_file" > "$temp_file"

    # Replace the original file
    mv "$temp_file" "$config_file"

    echo "✅ CrowdSec configuration added to dynamic config"
}

# Add CrowdSec configuration to existing dynamic config
add_crowdsec_config
    # HTTP to HTTPS redirect router
    main-app-router-redirect:
      rule: "Host(\`${ADMIN_SUBDOMAIN}.${DOMAIN}\`)"
      service: next-service
      entryPoints:
        - web
      middlewares:
        - redirect-to-https

    # Next.js router (handles everything except API and WebSocket paths)
    next-router:
      rule: "Host(\`${ADMIN_SUBDOMAIN}.${DOMAIN}\`) && !PathPrefix(\`/api/v1\`)"
      service: next-service
      entryPoints:
        - websecure
      middlewares:
        - security-headers
      tls:
        certResolver: letsencrypt

    # API router (handles /api/v1 paths)
    api-router:
      rule: "Host(\`${ADMIN_SUBDOMAIN}.${DOMAIN}\`) && PathPrefix(\`/api/v1\`)"
      service: api-service
      entryPoints:
        - websecure
      middlewares:
        - security-headers
      tls:
        certResolver: letsencrypt

    # WebSocket router
    ws-router:
      rule: "Host(\`${ADMIN_SUBDOMAIN}.${DOMAIN}\`)"
      service: api-service
      entryPoints:
        - websecure
      middlewares:
        - security-headers
      tls:
        certResolver: letsencrypt

    statiq-router-redirect:
      rule: "Host(\`${STATIC_PAGE_DOMAIN}.${DOMAIN}\`)"
      service: statiq-service
      entryPoints:
        - web
      middlewares:
        - redirect-to-https

    statiq-router:
      entryPoints:
        - websecure
      middlewares:
        - statiq
      service: statiq-service
      priority: 100
      rule: "Host(\`${STATIC_PAGE_DOMAIN}.${DOMAIN}\`)"

    # Add these lines for mcpauth
    # mcpauth http redirect router
    mcpauth-router-redirect:
      rule: "Host(\`oauth.${DOMAIN}\`)"
      service: mcpauth-service
      entryPoints:
        - web
      middlewares:
        - redirect-to-https

    # mcpauth router
    mcpauth:
      rule: "Host(\`oauth.${DOMAIN}\`)"
      service: mcpauth-service
      entryPoints:
        - websecure
      tls:
        certResolver: letsencrypt

  services:
    next-service:
      loadBalancer:
        servers:
          - url: "http://pangolin:3002" # Next.js server

    api-service:
      loadBalancer:
        servers:
          - url: "http://pangolin:3000" # API/WebSocket server

    statiq-service:
      loadBalancer:
        servers:
          - url: "noop@internal"

    mcpauth-service:
      loadBalancer:
        servers:
          - url: "http://mcpauth:11000"  # mcpauth auth server

    oauth-service:
      loadBalancer:
        servers:
          - url: "https://oauth.${DOMAIN}"
EOF

# Deployment info additions
cat >> /host-setup/DEPLOYMENT_INFO.txt << 'EOF'
└── crowdsec/
    ├── acquis.yaml
    ├── config.yaml
    └── profiles.yaml
📁 Additional:
./crowdsec_logs/          # Log volume for CrowdSec

🛡️ CrowdSec Notes:
- AppSec and log parsing is configured
- Prometheus and API are enabled
- CAPTCHA and remediation profiles are active
- Remember to get the bouncer API key after containers start:
  docker exec crowdsec cscli bouncers add traefik-bouncer
EOF

